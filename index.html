<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>课程学习助手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        #jsonpInput {
            flex: 1;
            height: 150px;
            margin-bottom: 10px;
        }

        #logOutput {
            width: 100%;
            height: 300px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }

        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .clear-button {
            padding: 5px 10px;
            background-color: #f8d7da;
            border: 1px solid #dc3545;
            color: #dc3545;
            cursor: pointer;
            border-radius: 4px;
        }

        .clear-button:hover {
            background-color: #dc3545;
            color: white;
        }

        .stop-button {
            background-color: #dc3545;
            color: white;
            border: 1px solid #dc3545;
        }

        .stop-button:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        /* 课程配置样式 */
        .course-config-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
            transition: box-shadow 0.2s;
        }

        .course-config-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .course-config-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .course-config-info {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .course-config-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        .course-config-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .course-config-loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }

        /* 课程学习进度样式 */
        .course-progress {
            margin-top: 10px;
            padding: 8px;
            background-color: #f0f8ff;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            font-size: 12px;
        }

        .course-progress.learning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }

        .course-progress.completed {
            background-color: #d4edda;
            border-left-color: #28a745;
        }

        .course-progress.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }

        .course-progress.stopped {
            background-color: #e2e3e5;
            border-left-color: #6c757d;
        }

        .progress-status {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .progress-details {
            color: #666;
            line-height: 1.4;
        }

        /* 获取课程列表按钮样式 */
        .get-course-list-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .get-course-list-btn:hover {
            background-color: #0056b3;
        }

        .get-course-list-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="instructions">
            <h2>使用说明</h2>
            <p>由于浏览器的安全限制，直接打开本页面会遇到跨域问题。请选择以下任一浏览器，按步骤操作：</p>

            <h3>Chrome浏览器：</h3>
            <ol>
                <li>关闭所有Chrome浏览器窗口</li>
                <li>创建一个空文件夹(例如: chrome_temp)</li>
                <li>使用以下命令启动Chrome：
                    <br>
                    <strong>Windows:</strong>
                    <br>
                    <code>"C:\Program Files\Google\Chrome\Application\chrome.exe" --disable-web-security --user-data-dir="C:\chrome_temp"</code>
                    <br>
                    <strong>MacOS:</strong>
                    <br>
                    <code>open -n -a "Google Chrome" --args --disable-web-security --user-data-dir="/Users/<USER>/chrome_temp"</code>
                </li>
            </ol>

            <p><strong>注意事项：</strong></p>
            <ul>
                <li>请根据实际安装路径调整浏览器执行文件的位置</li>
                <li>临时文件夹路径可以自定义，但必须是一个空文件夹</li>
                <li>启动后会看到浏览器提示"您使用的是不受支持的命令行标记 --disable-web-security"，这是正常现象</li>
                <li>如果使用MacOS，请将[用户名]替换为实际的用户名</li>
                <li>不建议使用IE浏览器，因为：
                    <ul>
                        <li>IE浏览器对现代Web标准支持不完善</li>
                        <li>可能存在兼容性问题</li>
                        <li>性能相对较差</li>
                        <li>Microsoft已不再支持IE浏览器</li>
                    </ul>
                </li>
                <li>推荐使用Chrome或搜狗浏览器运行本程序</li>
            </ul>

            <h3>最后一步：</h3>
            <p>在配置好的浏览器中打开本页面即可正常使用</p>
        </div>
        <h1>课程学习助手</h1>



        <div style="display: flex; gap: 30px; align-items: flex-start; margin-bottom: 20px;">
            <div>
                <h3 style="margin-bottom: 10px;">请选择培训类型：</h3>
                <select id="trainType" style="padding: 5px;" onchange="loadCourseConfigs()">
                    <option value="teacher">教师暑假</option>
                    <option value="student">师范生免试</option>
                </select>
            </div>
            <div>
                <h3 style="margin-bottom: 10px;">批量设置学习数量：</h3>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="text" id="batchResourceCount" placeholder="例如: 5,10,3,8 或单个数字"
                        style="padding: 5px; width: 200px;" value="5,5,5,5,5,9,8,5,5">
                    <button onclick="setBatchConfig()" style="padding: 5px 10px;">应用到全部</button>
                    <button onclick="resetAllConfigs()"
                        style="padding: 5px 10px; background-color: #f8d7da; border: 1px solid #dc3545; color: #dc3545;">重置</button>
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    💡 支持多个数字：用逗号分隔，按顺序应用给各课程（如：5,5,5,5,5,9,8,5,5）
                </div>
            </div>
        </div>

        <!-- 课程配置区域 -->
        <div id="courseConfigSection" style="margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h3 style="margin: 0;">课程学习配置：</h3>
                <div style="display: flex; gap: 10px;">
                    <button onclick="selectAllCourses()" style="padding: 5px 10px; font-size: 12px;">全选</button>
                    <button onclick="deselectAllCourses()" style="padding: 5px 10px; font-size: 12px;">全不选</button>
                    <span id="selectedCourseCount" style="font-size: 12px; color: #666;">已选择: 0 个课程</span>
                </div>
            </div>
            <div id="courseConfigContainer"
                style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 10px;">
                <!-- 课程配置项将在这里动态生成 -->
            </div>
        </div>
        <div>
            <h3>请输入JSONP响应数据:</h3>
            <div class="input-group">
                <textarea id="jsonpInput" placeholder="请输入JSONP响应数据..."></textarea>
                <button class="clear-button" onclick="clearJsonpInput()">清空输入</button>
            </div>
        </div>
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="get-course-list-btn" onclick="getCourseList()">获取课程列表</button>
            <button id="learningButton" onclick="toggleLearning()">开始学习</button>
            <button id="oneClickButton" onclick="oneClickLearning()" disabled
                style="background-color: #28a745; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                🚀 一键学习
            </button>
        </div>
        <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
            💡 一键学习：自动执行"应用到全部(默认) → 开始学习"
        </div>
        <div>
            <h3>执行日志:</h3>
            <div id="logOutput"></div>
        </div>
    </div>

    <script>
        // 全局变量（必须由用户通过JSONP数据提供）
        let macId = '';
        let macKey = '';
        let userId = '';
        let currentTrainId = '';

        // 处理用户提供的JSONP数据
        function processUserJsonpData() {
            // 从输入框获取用户提供的JSONP数据
            const jsonpInput = document.getElementById('jsonpInput');
            const jsonpData = jsonpInput.value.trim();

            if (!jsonpData) {
                log('请先在输入框中输入JSONP响应数据');
                return false;
            }

            try {
                // 提取JSONP数据
                const jsonStart = jsonpData.indexOf('({') + 1;
                const jsonEnd = jsonpData.lastIndexOf('})');
                const jsonStr = jsonpData.substring(jsonStart, jsonEnd + 1);
                const data = JSON.parse(jsonStr);

                // 更新认证信息
                if (data.$body) {
                    macKey = data.$body.mac_key;
                    macId = data.$body.access_token;
                    userId = data.$body.user_id;

                    log(`使用用户提供的JSONP数据更新认证信息:`);
                    log(`- macKey: ${macKey}`);
                    log(`- macId: ${macId}`);
                    log(`- userId: ${userId}`);

                    return true;
                }
            } catch (error) {
                log(`处理用户JSONP数据失败: ${error.message}`);
                log('请确保输入的是有效的JSONP响应数据');
            }

            return false;
        }
        const appId = 'e5649925-441d-4a53-b525-51a2f1c4e0a8';
        const TEACHER_TRAIN_ID = '10f7b3d6-e1c6-4a2e-ba76-e2f2af4674a5';
        const STUDENT_TRAIN_ID = '9b088c40-0824-43e0-b870-b94f617d4a37';
        const tags = '2025年暑期教师研修';
        const origin = '2025年暑期教师研修';

        // API基础URL常量
        const API_BASE_URLS = {
            TRAIN_API: 'https://s-file-1.ykt.cbern.com.cn/teach/api_static/trains/',
            COURSE_API: 'https://s-file-1.ykt.cbern.com.cn/teach/s_course/v2/',
            STUDY_RECORD_API: 'https://x-study-record-api.ykt.eduyun.cn/v1/',
            TRAIN_GATEWAY_API: 'https://elearning-train-gateway.ykt.eduyun.cn/v1/',
            TRAIN_GATEWAY_V2_API: 'https://elearning-train-gateway.ykt.eduyun.cn/v2/',
            ELEARNING_TRAIN_API: 'https://elearning-train-api.ykt.eduyun.cn/v1/'
        };

        // 课程配置管理器
        const CourseConfigManager = {
            configs: new Map(), // 存储每个课程的学习数量配置

            // 设置课程学习数量
            setCourseConfig(courseId, count) {
                if (this.validateCount(count)) {
                    this.configs.set(courseId, count);
                    return true;
                }
                return false;
            },

            // 获取课程学习数量
            getCourseConfig(courseId) {
                return this.configs.get(courseId) || null;
            },

            // 批量设置所有课程
            setAllCourses(count) {
                if (this.validateCount(count)) {
                    for (const courseId of this.configs.keys()) {
                        this.configs.set(courseId, count);
                    }
                    this.updateAllInputs(count);
                    return true;
                }
                return false;
            },

            // 验证数量
            validateCount(count) {
                return count === null || (Number.isInteger(count) && count > 0);
            },

            // 更新所有输入框
            updateAllInputs(count) {
                const inputs = document.querySelectorAll('.course-config-input');
                inputs.forEach(input => {
                    input.value = count || '';
                });
            },

            // 重置所有配置
            resetAll() {
                this.configs.clear();
                this.updateAllInputs('');
            }
        };

        // 学习状态管理变量
        let isLearning = false;
        let shouldStop = false;

        // 学习耗时记录变量
        let learningStartTime = null;
        let learningEndTime = null;

        // 格式化耗时显示
        function formatDuration(milliseconds) {
            const totalSeconds = Math.floor(milliseconds / 1000);
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            if (hours > 0) {
                return `${hours}小时${minutes}分钟${seconds}秒`;
            } else if (minutes > 0) {
                return `${minutes}分钟${seconds}秒`;
            } else {
                return `${seconds}秒`;
            }
        }

        // 多线程学习管理器
        const MultiThreadLearningManager = {
            // 活跃的学习线程
            activeThreads: new Map(),

            // 线程状态
            threadStatus: new Map(),

            // 开始多线程学习
            async startMultiThreadLearning() {
                log('🚀 启动多线程学习模式...');

                try {
                    await join();
                    const courseList = await getSelectedCourses();

                    if (!courseList || courseList.length === 0) {
                        log('错误: 未选择任何课程进行学习');
                        return;
                    }

                    log(`📚 开始学习选中的${courseList.length}个课程...`);

                    // 重置所有课程进度为等待状态
                    CourseProgressManager.resetAllProgress();

                    // 为每个课程创建学习线程
                    const learningPromises = courseList.map(course =>
                        this.createCourseThread(course)
                    );

                    // 等待所有线程完成
                    await Promise.allSettled(learningPromises);

                    log('🎉 所有课程学习线程完成！');

                } catch (error) {
                    log(`❌ 多线程学习启动失败: ${error.message}`);
                    throw error;
                }
            },

            // 为单个课程创建学习线程
            async createCourseThread(course) {
                const threadId = `thread_${course.courseId}`;

                try {
                    // 记录线程状态
                    this.threadStatus.set(threadId, {
                        courseId: course.courseId,
                        courseName: course.courseName,
                        status: 'starting',
                        startTime: new Date(),
                        progress: 0,
                        total: 0
                    });

                    // 更新UI进度显示
                    CourseProgressManager.updateCourseProgress(course.courseId, 'starting', '正在初始化...');

                    log(`🧵 [线程${threadId}] 开始学习: ${course.courseName}`);

                    // 更新UI进度显示为学习中
                    CourseProgressManager.updateCourseProgress(course.courseId, 'learning', '正在学习课程资源...');

                    // 执行单个课程的学习逻辑
                    await this.learnSingleCourse(course, threadId);

                    // 更新线程状态为完成
                    this.threadStatus.set(threadId, {
                        ...this.threadStatus.get(threadId),
                        status: 'completed',
                        endTime: new Date()
                    });

                    // 计算学习耗时
                    const duration = new Date() - this.threadStatus.get(threadId).startTime;
                    const durationStr = formatDuration(duration);

                    // 更新UI进度显示为完成
                    CourseProgressManager.updateCourseProgress(course.courseId, 'completed',
                        `学习完成 | 耗时: ${durationStr}`);

                    log(`✅ [线程${threadId}] 完成课程: ${course.courseName}`);

                } catch (error) {
                    // 更新线程状态为错误
                    this.threadStatus.set(threadId, {
                        ...this.threadStatus.get(threadId),
                        status: 'error',
                        error: error.message,
                        endTime: new Date()
                    });

                    // 更新UI进度显示为错误
                    CourseProgressManager.updateCourseProgress(course.courseId, 'error',
                        `学习失败: ${error.message}`);

                    log(`❌ [线程${threadId}] 课程学习失败: ${course.courseName} - ${error.message}`);
                } finally {
                    // 清理线程
                    this.activeThreads.delete(threadId);
                }
            },

            // 单个课程的学习逻辑（3线程并发优化版）
            async learnSingleCourse(course, threadId) {
                // 检查全局停止状态
                if (shouldStop) {
                    log(`🛑 [线程${threadId}] 学习已停止`);
                    return;
                }

                const resourceTotalCount = course.resourceTotalCount || 0;
                log(`📊 [线程${threadId}] 课程元数据显示资源总数: ${resourceTotalCount}`);

                const courses = await getResourceByCourseId(course.courseId);

                if (!courses || courses.length === 0) {
                    log(`⚠️ [线程${threadId}] ${course.courseName} 未找到可学习的资源`);
                    return;
                }

                log(`📋 [线程${threadId}] 实际找到 ${courses.length} 个可学习的资源`);

                // 获取该课程的学习数量配置
                const courseResourceCount = getCourseResourceCount(course.courseId);
                let num;
                let learningStrategy;

                if (courseResourceCount !== null) {
                    // 用户为该课程指定了数量
                    num = Math.min(courseResourceCount, courses.length);
                    learningStrategy = `课程配置 (${courseResourceCount}个)`;
                    if (courseResourceCount > courses.length) {
                        log(`⚠️ [线程${threadId}] 课程配置学习${courseResourceCount}个资源，但实际只有${courses.length}个资源，将学习全部${num}个资源`);
                    }
                } else {
                    // 用户未为该课程设置，学习所有实际资源
                    num = courses.length;
                    learningStrategy = '默认策略 (学习全部)';
                }

                log(`📝 [线程${threadId}] 学习策略: ${learningStrategy}`);
                log(`🎯 [线程${threadId}] 计划学习资源数: ${num}/${courses.length}`);
                log(`🚀 [线程${threadId}] 启动3个子线程并发学习资源...`);

                // 初始化课程级别的进度状态
                const courseProgress = {
                    total: num,
                    completed: 0,
                    subThreads: {
                        thread1: { completed: 0, total: 0 },
                        thread2: { completed: 0, total: 0 },
                        thread3: { completed: 0, total: 0 }
                    }
                };

                // 更新线程状态
                this.threadStatus.set(threadId, {
                    ...this.threadStatus.get(threadId),
                    status: 'learning',
                    total: num,
                    progress: 0,
                    courseProgress: courseProgress
                });

                // 更新UI进度显示
                CourseProgressManager.updateCourseProgress(course.courseId, 'learning',
                    `${learningStrategy} | 3线程并发 | 进度: 0/${num}`);

                // 将资源分配给3个子线程并发学习
                await this.learnWithMultipleThreads(course, courses, num, threadId, learningStrategy);

                // 完成课程学习后的处理
                const studyDetail = await getStudyDetail(course.courseId);
                if (studyDetail) {
                    const studyDetailJson = JSON.parse(studyDetail);
                    delete studyDetailJson.id;
                    delete studyDetailJson.client_id;
                    delete studyDetailJson.create_time;
                    delete studyDetailJson.update_time;
                    await async(JSON.stringify(studyDetailJson), course.courseId);
                }
            },

            // 3线程并发学习资源的核心方法
            async learnWithMultipleThreads(course, courses, num, threadId, learningStrategy) {
                // 将要学习的资源分配给3个子线程
                const resourcesToLearn = courses.slice(0, num);
                const subThread1Resources = [];
                const subThread2Resources = [];
                const subThread3Resources = [];

                // 使用轮询方式分配资源
                for (let i = 0; i < resourcesToLearn.length; i++) {
                    const threadIndex = i % 3;
                    if (threadIndex === 0) {
                        subThread1Resources.push({ resource: resourcesToLearn[i], index: i });
                    } else if (threadIndex === 1) {
                        subThread2Resources.push({ resource: resourcesToLearn[i], index: i });
                    } else {
                        subThread3Resources.push({ resource: resourcesToLearn[i], index: i });
                    }
                }

                // 更新子线程资源分配信息
                const courseProgress = this.threadStatus.get(threadId).courseProgress;
                courseProgress.subThreads.thread1.total = subThread1Resources.length;
                courseProgress.subThreads.thread2.total = subThread2Resources.length;
                courseProgress.subThreads.thread3.total = subThread3Resources.length;

                log(`🧵 [线程${threadId}] 资源分配: 子线程1(${subThread1Resources.length}个) | 子线程2(${subThread2Resources.length}个) | 子线程3(${subThread3Resources.length}个)`);

                // 创建3个并发的学习任务
                const learningTasks = [
                    this.learnResourcesInSubThread(course, courses, subThread1Resources, threadId, 1, learningStrategy),
                    this.learnResourcesInSubThread(course, courses, subThread2Resources, threadId, 2, learningStrategy),
                    this.learnResourcesInSubThread(course, courses, subThread3Resources, threadId, 3, learningStrategy)
                ];

                log(`🚀 [线程${threadId}] 3个子线程并发启动，开始学习...`);

                // 等待所有子线程完成
                const results = await Promise.allSettled(learningTasks);

                // 统计子线程完成情况
                const completedCount = results.filter(result => result.status === 'fulfilled').length;
                const failedCount = results.filter(result => result.status === 'rejected').length;

                log(`✅ [线程${threadId}] 所有子线程学习完成 | 成功: ${completedCount}/3 | 失败: ${failedCount}/3`);
            },

            // 子线程学习资源的方法
            async learnResourcesInSubThread(course, allCourses, resourcesWithIndex, parentThreadId, subThreadNumber, learningStrategy) {
                const subThreadId = `${parentThreadId}_sub${subThreadNumber}`;

                log(`🧵 [子线程${subThreadId}] 开始学习 ${resourcesWithIndex.length} 个资源`);

                for (const { resource, index } of resourcesWithIndex) {
                    // 检查全局停止状态
                    if (shouldStop) {
                        log(`🛑 [子线程${subThreadId}] 学习已停止`);
                        return;
                    }

                    try {
                        log(`📖 [子线程${subThreadId}] 正在学习资源 ${index + 1}: ${resource.title || '未知资源'}`);

                        await learningOneResource(allCourses, resource.resourceId, course.courseId, course.frontCoverUrl);

                        // 更新子线程进度
                        const courseProgress = this.threadStatus.get(parentThreadId).courseProgress;
                        courseProgress.subThreads[`thread${subThreadNumber}`].completed++;
                        courseProgress.completed++;

                        // 更新总进度
                        this.threadStatus.set(parentThreadId, {
                            ...this.threadStatus.get(parentThreadId),
                            progress: courseProgress.completed
                        });

                        // 更新UI进度显示
                        const sub1Progress = courseProgress.subThreads.thread1.completed;
                        const sub2Progress = courseProgress.subThreads.thread2.completed;
                        const sub3Progress = courseProgress.subThreads.thread3.completed;
                        const progressText = `${learningStrategy} | 3线程并发 | 总进度: ${courseProgress.completed}/${courseProgress.total} | 子线程进度: [${sub1Progress},${sub2Progress},${sub3Progress}]`;
                        CourseProgressManager.updateCourseProgress(course.courseId, 'learning', progressText);

                        log(`✅ [子线程${subThreadId}] 完成资源 ${index + 1}: ${resource.title || '未知资源'}`);

                    } catch (error) {
                        log(`❌ [子线程${subThreadId}] 学习资源失败 ${index + 1}: ${error.message}`);
                        // 继续学习下一个资源，不中断整个子线程
                    }

                    // 再次检查停止状态
                    if (shouldStop) {
                        log(`🛑 [子线程${subThreadId}] 学习已停止`);
                        return;
                    }
                }

                log(`✅ [子线程${subThreadId}] 所有资源学习完成`);
            },

            // 停止所有学习线程
            stopAllThreads() {
                log('🛑 正在停止所有学习线程...');

                // 设置全局停止标志
                shouldStop = true;

                // 记录停止的线程
                for (const [threadId, status] of this.threadStatus.entries()) {
                    if (status.status === 'learning' || status.status === 'starting') {
                        this.threadStatus.set(threadId, {
                            ...status,
                            status: 'stopped',
                            endTime: new Date()
                        });

                        // 更新UI进度显示为停止状态
                        CourseProgressManager.updateCourseProgress(status.courseId, 'stopped',
                            `学习已停止 | 进度: ${status.progress || 0}/${status.total || 0}`);

                        log(`🛑 [线程${threadId}] 已停止: ${status.courseName}`);
                    }
                }

                // 清理活跃线程
                this.activeThreads.clear();

                log('✅ 所有学习线程已停止');
            },

            // 获取学习状态统计
            getStatus() {
                const stats = {
                    total: this.threadStatus.size,
                    completed: 0,
                    learning: 0,
                    error: 0,
                    stopped: 0
                };

                for (const status of this.threadStatus.values()) {
                    stats[status.status]++;
                }

                return stats;
            },

            // 清理状态
            reset() {
                this.activeThreads.clear();
                this.threadStatus.clear();
            }
        };

        // 获取当前选中的培训类型
        function getSelectedTrainType() {
            return document.getElementById('trainType').value;
        }

        // 获取当前trainId
        function getCurrentTrainId() {
            return getSelectedTrainType() === 'student' ? STUDENT_TRAIN_ID : TEACHER_TRAIN_ID;
        }

        // 获取指定课程的学习数量配置
        function getCourseResourceCount(courseId) {
            return CourseConfigManager.getCourseConfig(courseId);
        }

        // 验证资源数量输入
        function validateResourceCount(count) {
            if (count === null || count === '') {
                return true; // 空值表示学习全部，这是有效的
            }
            const num = parseInt(count, 10);
            if (isNaN(num) || num < 1 || !Number.isInteger(num)) {
                return false;
            }
            return true;
        }

        // 更新课程配置
        function updateCourseConfig(courseId, value) {
            const input = document.querySelector(`[data-course-id="${courseId}"]`);
            const count = value.trim() === '' ? null : parseInt(value, 10);

            // 获取课程信息用于验证
            const maxResources = input ? parseInt(input.getAttribute('max')) : 999;

            if (validateResourceCount(count)) {
                // 检查是否超过最大资源数
                if (count !== null && count > maxResources) {
                    log(`警告: 课程学习数量 ${count} 超过了该课程的总资源数 ${maxResources}，已调整为 ${maxResources}`);
                    const adjustedCount = maxResources;
                    CourseConfigManager.setCourseConfig(courseId, adjustedCount);
                    if (input) input.value = adjustedCount;
                } else {
                    CourseConfigManager.setCourseConfig(courseId, count);
                    log(`课程配置已更新: ${getCourseName(courseId)} - 学习数量: ${count || '全部'}`);
                }
            } else {
                log(`警告: 学习数量设置无效，必须是正整数或留空`);
                // 重置输入框
                if (input) {
                    input.value = '';
                    CourseConfigManager.setCourseConfig(courseId, null);
                }
            }
        }

        // 获取课程名称（用于日志显示）
        function getCourseName(courseId) {
            const configItem = document.querySelector(`[data-course-id="${courseId}"]`)?.closest('.course-config-item');
            if (configItem) {
                const header = configItem.querySelector('.course-config-header');
                return header ? header.textContent : courseId;
            }
            return courseId;
        }

        // 课程进度管理器
        const CourseProgressManager = {
            // 更新课程学习进度
            updateCourseProgress(courseId, status, details) {
                const progressDiv = document.getElementById(`progress-${courseId}`);
                const statusDiv = document.getElementById(`status-${courseId}`);
                const detailsDiv = document.getElementById(`details-${courseId}`);

                if (!progressDiv || !statusDiv || !detailsDiv) {
                    return; // 如果找不到进度显示元素，直接返回
                }

                // 显示进度区域
                progressDiv.style.display = 'block';

                // 移除所有状态类
                progressDiv.classList.remove('learning', 'completed', 'error', 'stopped');

                // 根据状态添加对应的样式类和图标
                let statusText = '';
                let statusIcon = '';

                switch (status) {
                    case 'waiting':
                        statusIcon = '⏳';
                        statusText = '等待开始';
                        break;
                    case 'starting':
                        progressDiv.classList.add('learning');
                        statusIcon = '🚀';
                        statusText = '正在启动';
                        break;
                    case 'learning':
                        progressDiv.classList.add('learning');
                        statusIcon = '📚';
                        statusText = '学习中';
                        break;
                    case 'completed':
                        progressDiv.classList.add('completed');
                        statusIcon = '✅';
                        statusText = '已完成';
                        break;
                    case 'error':
                        progressDiv.classList.add('error');
                        statusIcon = '❌';
                        statusText = '出现错误';
                        break;
                    case 'stopped':
                        progressDiv.classList.add('stopped');
                        statusIcon = '⏹️';
                        statusText = '已停止';
                        break;
                    default:
                        statusIcon = '❓';
                        statusText = status;
                }

                statusDiv.textContent = `${statusIcon} ${statusText}`;
                detailsDiv.textContent = details || '';
            },

            // 隐藏所有课程的进度显示
            hideAllProgress() {
                const progressDivs = document.querySelectorAll('.course-progress');
                progressDivs.forEach(div => {
                    div.style.display = 'none';
                });
            },

            // 显示所有课程的进度显示
            showAllProgress() {
                const progressDivs = document.querySelectorAll('.course-progress');
                progressDivs.forEach(div => {
                    div.style.display = 'block';
                });
            },

            // 重置所有课程进度为等待状态
            resetAllProgress() {
                const courseInputs = document.querySelectorAll('.course-config-input');
                courseInputs.forEach(input => {
                    const courseId = input.getAttribute('data-course-id');
                    this.updateCourseProgress(courseId, 'waiting', '准备中...');
                });
            }
        };

        // 批量设置配置
        // 批量设置配置（支持逗号分隔的多个数字）
        function setBatchConfig() {
            const input = document.getElementById('batchResourceCount');
            const value = input.value.trim();

            if (value === '') {
                // 空值表示全部课程设置为学习全部资源
                const courseInputs = document.querySelectorAll('.course-config-input');
                let updatedCount = 0;

                courseInputs.forEach(courseInput => {
                    const courseId = courseInput.getAttribute('data-course-id');
                    courseInput.value = '';
                    CourseConfigManager.setCourseConfig(courseId, null);
                    updatedCount++;
                });

                log(`📋 批量设置完成: ${updatedCount}个课程的学习数量已设置为 全部`);
                return;
            }

            // 解析输入值：支持单个数字或逗号分隔的多个数字
            let numbers = [];
            if (value.includes(',')) {
                // 多个数字，按逗号分隔
                const parts = value.split(',');
                for (let part of parts) {
                    const trimmed = part.trim();
                    if (trimmed === '') {
                        numbers.push(null); // 空值表示学习全部
                    } else {
                        const num = parseInt(trimmed, 10);
                        if (isNaN(num) || num < 1) {
                            alert(`❌ 输入格式错误: "${trimmed}" 不是有效的数字。请输入大于0的整数或留空。`);
                            return;
                        }
                        numbers.push(num);
                    }
                }
            } else {
                // 单个数字
                const num = parseInt(value, 10);
                if (isNaN(num) || num < 1) {
                    alert(`❌ 输入格式错误: "${value}" 不是有效的数字。请输入大于0的整数。`);
                    return;
                }
                numbers.push(num);
            }

            // 获取所有课程输入框并按顺序应用设置
            const courseInputs = document.querySelectorAll('.course-config-input');
            let updatedCount = 0;
            let appliedSettings = [];

            courseInputs.forEach((courseInput, index) => {
                const courseId = courseInput.getAttribute('data-course-id');
                const courseName = courseInput.closest('.course-config-item').querySelector('.course-config-header').textContent;
                const maxResources = parseInt(courseInput.getAttribute('max'));

                // 获取对应的数字（如果数字不够，使用最后一个数字）
                const numberIndex = Math.min(index, numbers.length - 1);
                const count = numbers[numberIndex];

                if (count !== null && count > maxResources) {
                    // 如果设置的数量超过该课程的最大资源数，使用最大资源数
                    courseInput.value = maxResources;
                    CourseConfigManager.setCourseConfig(courseId, maxResources);
                    appliedSettings.push(`${courseName}: ${maxResources} (限制为最大值)`);
                } else {
                    courseInput.value = count || '';
                    CourseConfigManager.setCourseConfig(courseId, count);
                    appliedSettings.push(`${courseName}: ${count || '全部'}`);
                }
                updatedCount++;
            });

            // 显示详细的应用结果
            log(`📋 批量设置完成: ${updatedCount}个课程的学习数量已设置`);
            log(`📊 应用详情:`);
            appliedSettings.forEach(setting => {
                log(`  • ${setting}`);
            });

            if (numbers.length < courseInputs.length) {
                log(`💡 提示: 输入了${numbers.length}个数字，共${courseInputs.length}个课程。后面的课程使用最后一个数字 ${numbers[numbers.length - 1] || '全部'}`);
            }
        }

        // 重置所有配置
        function resetAllConfigs() {
            CourseConfigManager.resetAll();
            document.getElementById('batchResourceCount').value = '';
            log('已重置所有课程配置');
        }

        // 加载课程配置（当培训类型改变时调用）
        async function loadCourseConfigs() {
            const container = document.getElementById('courseConfigContainer');
            if (container) {
                container.innerHTML = '<div class="course-config-loading">正在加载课程列表...</div>';
            }

            // 重置配置管理器
            CourseConfigManager.resetAll();

            try {
                // 重新获取课程列表并生成UI
                await loadCourseListForConfig();
            } catch (error) {
                if (container) {
                    container.innerHTML = '<div class="course-config-loading">加载课程列表失败，请检查网络连接</div>';
                }
                log(`加载课程配置失败: ${error.message}`);
            }
        }

        // 专门用于加载课程配置的函数（不需要认证）
        async function loadCourseListForConfig() {
            const trainType = getSelectedTrainType();
            const currentTrainId = getCurrentTrainId();
            const url = trainType === 'student'
                ? `${API_BASE_URLS.TRAIN_API}${currentTrainId}/train_courses.json`
                : `${API_BASE_URLS.TRAIN_API}2025sqpx/train_courses.json`;

            log(`正在加载课程列表: ${url}`);

            try {
                // 由于跨域限制，先尝试使用JSONP方式，如果失败则显示预设课程
                const response = await fetch(url, {
                    mode: 'cors',
                    credentials: 'omit'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const courseArray = Array.isArray(data) ? data : data.data || [];

                // 处理课程数据
                const processedData = courseArray.map(course => ({
                    courseId: course.course_id,
                    courseName: course.title,
                    resourceTotalCount: parseInt(course.resource_total_count) || 0,
                    frontCoverUrl: course.front_cover_url
                })).filter(course => course.courseId);

                if (processedData.length === 0) {
                    throw new Error('没有找到有效的课程');
                }

                // 生成课程配置UI
                generateCourseConfigUI(processedData);
                log(`已加载${processedData.length}个课程的配置界面`);

                return processedData;

            } catch (error) {
                log(`无法直接获取课程列表 (${error.message})，使用预设课程列表`);
                // 使用预设的9个课程
                const presetCourses = getPresetCourses(trainType);
                generateCourseConfigUI(presetCourses);
                log(`已加载${presetCourses.length}个预设课程的配置界面`);
                return presetCourses;
            }
        }

        // 注意：所有课程配置现在都从接口动态获取，不再使用预设课程列表

        // 课程选择管理器
        const CourseSelectionManager = {
            selectedCourses: new Set(), // 存储选中的课程ID

            // 设置课程选择状态
            setCourseSelection(courseId, selected) {
                if (selected) {
                    this.selectedCourses.add(courseId);
                } else {
                    this.selectedCourses.delete(courseId);
                }
                this.updateSelectedCount();
            },

            // 获取课程选择状态
            isCourseSelected(courseId) {
                return this.selectedCourses.has(courseId);
            },

            // 获取所有选中的课程ID
            getSelectedCourses() {
                return Array.from(this.selectedCourses);
            },

            // 全选
            selectAll() {
                const checkboxes = document.querySelectorAll('.course-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    this.selectedCourses.add(checkbox.getAttribute('data-course-id'));
                });
                this.updateSelectedCount();
            },

            // 全不选
            deselectAll() {
                const checkboxes = document.querySelectorAll('.course-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    this.selectedCourses.delete(checkbox.getAttribute('data-course-id'));
                });
                this.updateSelectedCount();
            },

            // 更新选中数量显示
            updateSelectedCount() {
                const countElement = document.getElementById('selectedCourseCount');
                if (countElement) {
                    countElement.textContent = `已选择: ${this.selectedCourses.size} 个课程`;
                }
            },

            // 重置选择状态
            reset() {
                this.selectedCourses.clear();
                this.updateSelectedCount();
            }
        };

        // 更新课程选择状态
        function updateCourseSelection(courseId, selected) {
            CourseSelectionManager.setCourseSelection(courseId, selected);
        }

        // 全选课程
        function selectAllCourses() {
            CourseSelectionManager.selectAll();
        }

        // 全不选课程
        function deselectAllCourses() {
            CourseSelectionManager.deselectAll();
        }

        // 获取课程数据（不重新生成UI）
        async function getCourseDataOnly() {
            const trainType = getSelectedTrainType();
            const currentTrainId = getCurrentTrainId();
            const url = trainType === 'student'
                ? `${API_BASE_URLS.TRAIN_API}${currentTrainId}/train_courses.json`
                : `${API_BASE_URLS.TRAIN_API}2025sqpx/train_courses.json`;

            try {
                const response = await request(url);
                if (!response) {
                    throw new Error('获取课程列表失败');
                }

                // 确保response是一个数组
                const data = Array.isArray(response) ? response : response.data || [];

                // 处理课程数据，确保字段名称与Java版本一致
                const processedData = data.map(course => ({
                    courseId: course.course_id,
                    courseName: course.title,
                    resourceTotalCount: parseInt(course.resource_total_count) || 0,
                    frontCoverUrl: course.front_cover_url
                })).filter(course => course.courseId); // 只保留有courseId的课程

                return processedData;
            } catch (error) {
                log(`获取课程数据失败: ${error.message}`);
                throw error;
            }
        }

        // 获取选中的课程列表（用于学习功能）
        async function getSelectedCourses() {
            // 获取选中的课程ID
            const selectedCourseIds = CourseSelectionManager.getSelectedCourses();

            log(`🔍 调试信息: 当前选中的课程ID数量: ${selectedCourseIds.length}`);
            log(`🔍 调试信息: 选中的课程ID: ${selectedCourseIds.join(', ')}`);

            if (selectedCourseIds.length === 0) {
                throw new Error('请至少选择一个课程进行学习');
            }

            // 获取所有课程数据（不重新生成UI）
            const allCourses = await getCourseDataOnly();

            // 过滤出选中的课程
            const selectedCourses = allCourses.filter(course =>
                selectedCourseIds.includes(course.courseId)
            );

            log(`已选择${selectedCourses.length}个课程进行学习:`);
            selectedCourses.forEach((course, index) => {
                log(`${index + 1}. ${course.courseName} (${course.courseId})`);
            });

            return selectedCourses;
        }

        // 获取课程列表的主函数
        async function getCourseList() {
            const button = document.querySelector('.get-course-list-btn');

            try {
                // 首先处理用户提供的JSONP数据
                log('处理用户提供的JSONP认证数据...');
                const jsonpSuccess = processUserJsonpData();

                if (!jsonpSuccess) {
                    log('❌ 请先在输入框中输入有效的JSONP响应数据');
                    alert('请先在输入框中输入JSONP响应数据，然后再点击获取课程列表');
                    return;
                }

                // 验证认证信息是否完整
                if (!userId || !macId || !macKey) {
                    log('❌ 认证信息不完整，请检查JSONP数据格式');
                    alert('认证信息不完整，请检查JSONP数据格式');
                    return;
                }

                log('✅ 认证信息验证成功，开始获取课程列表...');

                // 禁用按钮
                button.disabled = true;
                button.textContent = '获取中...';

                log('开始获取课程列表...');

                // 获取培训配置数据并生成课程配置UI
                const trainCourses = await getTrainingConfig();
                generateCourseConfigUI(trainCourses);
                log(`✅ 成功获取并生成${trainCourses.length}个课程的配置界面`);

            } catch (error) {
                log(`❌ 获取课程列表失败: ${error.message}`);
                alert(`获取课程列表失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = '获取课程列表';
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', function () {
            log('页面加载完成');
            log('⚠️ 重要提醒：必须先在输入框中输入有效的JSONP响应数据才能使用学习功能');

            // 为JSONP输入框添加事件监听器
            const jsonpInput = document.getElementById('jsonpInput');
            if (jsonpInput) {
                // 监听输入变化，更新一键学习按钮状态
                jsonpInput.addEventListener('input', updateOneClickButtonState);
                jsonpInput.addEventListener('paste', function () {
                    // 粘贴后稍微延迟更新状态，确保内容已经粘贴完成
                    setTimeout(updateOneClickButtonState, 100);
                });
            }

            // 初始化一键学习按钮状态
            updateOneClickButtonState();
        });

        // 日志输出函数
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            logOutput.innerHTML += message + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            const logOutput = document.getElementById('logOutput');
            logOutput.innerHTML = '';
        }

        // 清空JSONP输入
        function clearJsonpInput() {
            const jsonpInput = document.getElementById('jsonpInput');
            jsonpInput.value = '';
            // 更新一键学习按钮状态
            updateOneClickButtonState();
        }

        // 随机字符串生成
        function randomStr(length) {
            const characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            return result;
        }

        // 生成UUID的兼容函数
        function generateUUID(withDash = true) {
            let d = new Date().getTime();
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                let r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
            });
            return withDash ? uuid : uuid.replace(/-/g, '');
        }

        // 生成nonce
        function nonce(diff) {
            return `${Date.now() + diff}:${randomStr(8)}`;
        }

        // HMAC-SHA256签名
        async function getSign(url, nonce, methodType, macKey) {
            const uri = new URL(url);
            const relativePath = uri.pathname + (uri.search || '');
            const authority = uri.host;
            const message = `${nonce}\n${methodType.toUpperCase()}\n${relativePath}\n${authority}\n`;

            const encoder = new TextEncoder();
            const key = await crypto.subtle.importKey(
                'raw',
                encoder.encode(macKey),
                { name: 'HMAC', hash: 'SHA-256' },
                false,
                ['sign']
            );

            const signature = await crypto.subtle.sign(
                'HMAC',
                key,
                encoder.encode(message)
            );

            return btoa(String.fromCharCode(...new Uint8Array(signature)));
        }

        // 获取认证Cookie
        async function getCookie(methodType, url) {
            // 检查认证信息
            if (!macId || !macKey) {
                throw new Error('认证信息不完整，请先输入JSONP数据');
            }

            const diff = -Math.floor(Math.random() * (600 - 500) + 500);
            const nonceStr = nonce(diff);
            const mac = await getSign(url, nonceStr, methodType, macKey);
            return `MAC id="${macId}",nonce="${nonceStr}",mac="${mac}"`;
        }

        // 加载JSONP
        function loadJSONP(url) {
            return new Promise((resolve, reject) => {
                const callbackName = 'jsonpCallback_' + Math.random().toString(36).substr(2, 9);
                window[callbackName] = function (data) {
                    document.head.removeChild(script);
                    delete window[callbackName];
                    resolve(data);
                };

                const script = document.createElement('script');
                script.src = url + (url.includes('?') ? '&' : '?') + 'callback=' + callbackName;
                script.onerror = () => {
                    document.head.removeChild(script);
                    delete window[callbackName];
                    reject(new Error('JSONP request failed'));
                };
                document.head.appendChild(script);
            });
        }

        // HTTP请求封装
        async function request(url, method = 'GET', body = null, extraHeaders = {}, enableTiming = false) {
            const startTime = Date.now();

            const cookie = await getCookie(method, url);
            const headers = {
                'Authorization': cookie,
                'sdp-app-id': appId,
                'Content-Type': 'application/json',
                ...extraHeaders
            };

            const options = {
                method,
                headers,
                mode: 'cors',
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            try {
                const response = await fetch(url, options);
                const endTime = Date.now();
                const duration = endTime - startTime;

                // 如果启用耗时统计，输出日志
                if (enableTiming) {
                    const urlPath = new URL(url).pathname;
                    const durationInSeconds = (duration / 1000).toFixed(2);
                    log(`[请求耗时] ${method} ${urlPath} - ${durationInSeconds}秒 (状态: ${response.status})`);
                }

                // 记录响应状态
                // log(`请求 ${url} 状态: ${response.status}`);

                // 如果响应为空，直接返回null
                const text = await response.text();
                if (!text) {
                    return null;
                }

                // 尝试解析JSON
                try {
                    return JSON.parse(text);
                } catch (e) {
                    // 如果不是JSON格式，直接返回文本
                    return text;
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (enableTiming) {
                    const urlPath = new URL(url).pathname;
                    const durationInSeconds = (duration / 1000).toFixed(2);
                    log(`[请求耗时] ${method} ${urlPath} - ${durationInSeconds}秒 (失败: ${error.message})`);
                }

                log(`请求错误: ${error.message}`);
                throw error;
            }
        }

        // 获取培训课程列表
        async function trainCourses() {
            const trainType = getSelectedTrainType();
            const currentTrainId = getCurrentTrainId();
            const url = trainType === 'student'
                ? `${API_BASE_URLS.TRAIN_API}${currentTrainId}/train_courses.json`
                : `${API_BASE_URLS.TRAIN_API}2025sqpx/train_courses.json`;
            try {
                const response = await request(url);
                if (!response) {
                    throw new Error('获取课程列表失败');
                }

                // 确保response是一个数组
                const data = Array.isArray(response) ? response : response.data || [];

                // 详细的课程列表日志输出
                log('获取到的课程列表:');
                data.forEach((course, index) => {
                    log(`${index + 1}. 课程名称: ${course.title || '未知'}`);
                    log(`   课程ID: ${course.course_id || '未知'}`);
                    log(`   资源总数: ${course.resource_total_count || 0}`);
                });

                // 处理课程数据，确保字段名称与Java版本一致
                const processedData = data.map(course => ({
                    courseId: course.course_id,
                    courseName: course.title,
                    resourceTotalCount: parseInt(course.resource_total_count) || 0,
                    frontCoverUrl: course.front_cover_url
                })).filter(course => course.courseId); // 只保留有courseId的课程

                if (processedData.length === 0) {
                    log('警告: 没有找到有效的课程');
                } else {
                    // 生成课程配置UI
                    generateCourseConfigUI(processedData);
                }

                return processedData;

            } catch (error) {
                log(`获取课程列表错误: ${error.message}`);
                throw error;
            }
        }

        // 生成课程配置UI
        function generateCourseConfigUI(courseList) {
            const container = document.getElementById('courseConfigContainer');

            if (!container) {
                log('错误: 无法找到课程配置容器');
                return;
            }

            // 清空现有内容
            container.innerHTML = '';

            // 重置课程选择状态
            CourseSelectionManager.reset();

            // 为每个课程创建配置项
            courseList.forEach((course, index) => {
                // 只有在课程配置不存在时才初始化为null，保留已有配置
                if (!CourseConfigManager.configs.has(course.courseId)) {
                    CourseConfigManager.configs.set(course.courseId, null);
                }

                // 获取当前配置值
                const currentConfig = CourseConfigManager.configs.get(course.courseId);
                const inputValue = currentConfig !== null ? currentConfig : '';

                const configItem = document.createElement('div');
                configItem.className = 'course-config-item';
                configItem.innerHTML = `
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <input type="checkbox"
                               class="course-checkbox"
                               data-course-id="${course.courseId}"
                               checked
                               onchange="updateCourseSelection('${course.courseId}', this.checked)"
                               style="margin-right: 8px; transform: scale(1.2);">
                        <div class="course-config-header" style="margin: 0;">${course.courseName}</div>
                    </div>
                    <div class="course-config-info">课程ID: ${course.courseId} | 总资源数: ${course.resourceTotalCount}</div>
                    <input type="number"
                           class="course-config-input"
                           data-course-id="${course.courseId}"
                           placeholder="留空学习全部资源"
                           min="1"
                           max="${course.resourceTotalCount}"
                           value="${inputValue}"
                           onchange="updateCourseConfig('${course.courseId}', this.value)">
                    <div class="course-progress" id="progress-${course.courseId}" style="display: none;">
                        <div class="progress-status" id="status-${course.courseId}">等待开始</div>
                        <div class="progress-details" id="details-${course.courseId}">准备中...</div>
                    </div>
                `;
                container.appendChild(configItem);

                // 默认选中所有课程
                CourseSelectionManager.setCourseSelection(course.courseId, true);
            });

            log(`已生成${courseList.length}个课程的配置界面`);
        }

        // 获取课程资源
        async function getResourceByCourseId(courseId) {
            const url = `${API_BASE_URLS.COURSE_API}business_courses/${courseId}/course_relative_infos/zh-CN.json`;
            const data = await request(url);
            const activitySetId = data.course_detail.activity_set_id;
            return await fullJson(activitySetId, courseId);
        }

        // 获取完整课程JSON
        async function fullJson(activitySetId, courseId) {
            const url = `${API_BASE_URLS.COURSE_API}activity_sets/${activitySetId}/fulls.json`;
            const data = await request(url);
            const courses = [];
            traverseNodes(data, data.nodes, courses);
            courses.forEach(course => course.courseId = courseId);
            return courses;
        }

        // 遍历节点
        function traverseNodes(respJson, nodes, courses) {
            for (const node of nodes) {
                if (!node.child_nodes || node.child_nodes.length === 0) {
                    const relations = node.relations;
                    if (relations && relations.activity && relations.activity.activity_resources) {
                        const resources = relations.activity.activity_resources;
                        if (resources.length > 0) {
                            const resource = resources[0];

                            // 添加空值检查和资源有效性验证
                            if (resource && resource.resource_id && resource.study_time && resource.study_time > 0) {
                                // 安全地获取title，只处理有标题的资源
                                let title = null;

                                // 尝试多种方式获取标题
                                if (resource.video_extend && resource.video_extend.title) {
                                    title = resource.video_extend.title;
                                } else if (resource.title) {
                                    title = resource.title;
                                } else if (resource.name) {
                                    title = resource.name;
                                } else if (resource.display_name) {
                                    title = resource.display_name;
                                } else if (resource.resource_name) {
                                    title = resource.resource_name;
                                } else if (resource.video_extend && resource.video_extend.name) {
                                    title = resource.video_extend.name;
                                }

                                // 只处理有标题的资源，跳过无标题的资源
                                if (title && title.trim()) {

                                    courses.push({
                                        courseName: respJson.activity_set_name,
                                        title: title,
                                        activityId: resource.activity_id,
                                        resourceId: resource.resource_id,
                                        studyTime: resource.study_time || 0
                                    });
                                } else {
                                    // 跳过无标题的资源（不记录日志，因为这是正常的筛选）
                                    console.debug('跳过无标题资源:', resource.resource_id);
                                }
                            } else {
                                // 跳过无效资源（无study_time或study_time为0的资源）
                                console.debug('跳过无效资源:', resource ? resource.resource_id : 'null resource');
                            }
                        }
                    }
                } else {
                    traverseNodes(respJson, node.child_nodes, courses);
                }
            }
        }

        // 学习单个资源
        async function learningOneResource(courses, resourceId, courseId, cover) {
            // 在函数开始时检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            const course = courses.find(c => c.resourceId === resourceId);
            if (!course) return;

            await learningByResourceId(course.resourceId, course.studyTime + 1);

            // 在API调用后检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await getPosition(course.resourceId);

            // 再次检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            log(`完成学习: ${course.courseName} - ${course.title}`);

            let studyDetail = await getStudyDetail(courseId);
            let detailJson;

            // 学过的课程，这里每次都新学
            if (studyDetail && studyDetail !== 'null') {
                try {
                    detailJson = JSON.parse(studyDetail);
                    delete detailJson.id;
                    delete detailJson.client_id;
                    delete detailJson.create_time;
                    delete detailJson.update_time;

                    let ext_info = {};
                    try {
                        ext_info = JSON.parse(detailJson.ext_info || '{}');
                    } catch (e) {
                        ext_info = {};
                    }

                    // 重写last_learning_activity
                    ext_info.last_learning_activity = {
                        activity_id: course.activityId,
                        title: course.title
                    };

                    // 更新activity_last_learning_resource
                    if (!ext_info.activity_last_learning_resource) {
                        ext_info.activity_last_learning_resource = {};
                    }
                    ext_info.activity_last_learning_resource[course.activityId] = course.resourceId;

                    // 更新activity_progress
                    if (!ext_info.activity_progress) {
                        ext_info.activity_progress = {};
                    }
                    ext_info.activity_progress[course.activityId] = 2;

                    // 更新resource_progress
                    if (!ext_info.resource_progress) {
                        ext_info.resource_progress = {};
                    }
                    ext_info.resource_progress[course.resourceId] = 2;

                    // 更新resource_max_pos
                    if (!ext_info.resource_max_pos) {
                        ext_info.resource_max_pos = {};
                    }
                    ext_info.resource_max_pos[course.resourceId] = {
                        pos: course.studyTime + 1,
                        type: "video"
                    };

                    // 确保其他必要字段存在
                    ext_info.cv = ext_info.cv || 1;
                    ext_info.platform = ext_info.platform || 'web';
                    ext_info.tags = ext_info.tags || tags;
                    ext_info.origin = ext_info.origin || origin;
                    ext_info.cover = ext_info.cover || cover;
                    ext_info.miniwork_progress = ext_info.miniwork_progress || {};
                    ext_info.activity_exam_progress = ext_info.activity_exam_progress || {};
                    ext_info.activity_event = ext_info.activity_event || {};
                    ext_info.resource_study_time_ignore = ext_info.resource_study_time_ignore || [];
                    ext_info.additional_params = ext_info.additional_params || {
                        library_id: 'bb042e69-9a11-49a1-af22-0c3fab2e92b9'
                    };

                    detailJson.ext_info = JSON.stringify(ext_info);
                    detailJson.progress = (detailJson.progress || 0) + 3;

                } catch (error) {
                    log(`处理已有学习记录时出错: ${error.message}`);
                    detailJson = createNewStudyDetail(course, cover);
                }
            } else {
                detailJson = createNewStudyDetail(course, cover);
            }

            // 在最后的API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await postStudyDetail(detailJson, courseId);

            // 在最后一个API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return;
            }

            await async(JSON.stringify(detailJson), courseId);
        }

        // 创建新的学习详情
        function createNewStudyDetail(course, cover) {
            return {
                user_id: userId,
                resource_id: course.courseId,
                resource_name: course.courseName,
                resource_type: 't_course',
                catalog_type: 'teacherTraining',
                topic_type: course.courseId,
                progress: 1,
                status: 1,
                ext_info: JSON.stringify({
                    cv: 1,
                    platform: 'web',
                    tags,
                    origin,
                    cover,
                    last_learning_activity: {
                        activity_id: course.activityId,
                        title: course.title
                    },
                    activity_last_learning_resource: {
                        [course.activityId]: course.resourceId
                    },
                    activity_progress: {
                        [course.activityId]: 2
                    },
                    resource_progress: {
                        [course.resourceId]: 2
                    },
                    resource_max_pos: {
                        [course.resourceId]: {
                            pos: course.studyTime + 1,
                            type: 'video'
                        }
                    },
                    miniwork_progress: {},
                    activity_exam_progress: {},
                    activity_event: {},
                    resource_study_time_ignore: [],
                    additional_params: {
                        library_id: 'bb042e69-9a11-49a1-af22-0c3fab2e92b9'
                    }
                })
            };
        }

        // 更新学习位置
        async function learningByResourceId(resourceId, position) {
            // 检查认证信息
            if (!userId) {
                throw new Error('用户未登录，请先输入JSONP数据');
            }

            // 在API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return null;
            }

            const url = `${API_BASE_URLS.STUDY_RECORD_API}resource_learning_positions/${resourceId}/${userId}`;
            await request(url, 'PUT', { position }, {}, true);

            // 在第二个API调用前检查停止状态
            if (shouldStop) {
                log('学习已停止');
                return null;
            }

            return await getPosition(resourceId);
        }

        // 获取学习位置
        async function getPosition(resourceId) {
            // 检查认证信息
            if (!userId) {
                throw new Error('用户未登录，请先输入JSONP数据');
            }

            const url = `${API_BASE_URLS.STUDY_RECORD_API}resource_learning_positions/${resourceId}/${userId}`;
            return await request(url, 'GET', null, {}, true);
        }

        // 获取学习详情
        async function getStudyDetail(courseId) {
            // 检查认证信息
            if (!userId) {
                throw new Error('用户未登录，请先输入JSONP数据');
            }

            const url = `${API_BASE_URLS.STUDY_RECORD_API}study_details/${courseId}/${userId}`;
            try {
                const result = await request(url);
                return JSON.stringify(result);
            } catch (error) {
                return null;
            }
        }

        // 提交学习详情
        async function postStudyDetail(detail, courseId) {
            // 检查认证信息
            if (!userId) {
                throw new Error('用户未登录，请先输入JSONP数据');
            }

            const url = `${API_BASE_URLS.STUDY_RECORD_API}study_details`;
            return await request(url, 'POST', detail, {}, true);
        }

        // 同步进度
        async function async(body, courseId) {
            const url = `${API_BASE_URLS.TRAIN_GATEWAY_API}spi/trains/${getCurrentTrainId()}/courses/${courseId}/progress/actions/async`;
            const headers = {
                'Host': 'elearning-train-gateway.ykt.eduyun.cn',
                'device_id': (crypto.randomUUID?.() || generateUUID()).toLowerCase(),
                'sdp-xpath-id': (crypto.randomUUID?.() || generateUUID()).toLowerCase()
            };
            return await request(url, 'POST', JSON.parse(body), headers, true);
        }

        // 加入培训
        async function join() {
            const currentTrainId = getCurrentTrainId();
            const url = `${API_BASE_URLS.TRAIN_GATEWAY_V2_API}spi/trains/${currentTrainId}/actions/async_joins`;
            const cookie = await getCookie('post', url);
            const headers = {
                'Authorization': cookie + '1',
                'sdp-app-id': appId,
                'Host': 'elearning-train-gateway.ykt.eduyun.cn'
            };
            const response = await request(url, 'POST', null, headers);
            log('已加入培训');
            return response;
        }











        // 解析JSONP数据的公共函数
        function parseJsonpData() {
            const jsonpInput = document.getElementById('jsonpInput').value;
            if (!jsonpInput) {
                throw new Error('请输入JSONP响应数据');
            }

            try {
                // 清理输入数据
                const cleanInput = jsonpInput.replace(/\s+/g, '');
                // 修改正则表达式以适应更复杂的函数名
                const match = cleanInput.match(/^[\w_]+\((.*)\)$/);

                if (!match) {
                    throw new Error('JSONP格式不正确');
                }

                const jsonStr = match[1];
                const data = JSON.parse(jsonStr);

                if (!data.$body || !data.$body.user_id || !data.$body.mac_key || !data.$body.access_token) {
                    throw new Error('JSONP数据格式不正确，缺少必要的字段');
                }

                // 设置全局变量
                userId = data.$body.user_id;
                macKey = data.$body.mac_key;
                macId = data.$body.access_token;

                log(`用户ID: ${userId}`);
                log('认证信息获取成功');

                return true;
            } catch (error) {
                log(`解析JSONP数据失败: ${error.message}`);
                throw error;
            }
        }



        // 动态获取的课程配置数据
        let currentTrainingCourses = [];
        let courseIdToNameMapping = {};

        // 移除转换比率，API返回的数据已经是正确的学时数

        // 获取培训配置数据
        async function getTrainingConfig() {
            const trainConfigUrl = 'https://s-file-2.ykt.cbern.com.cn/teach/api_static/trains/2025sqpx/train_courses.json';

            try {
                const response = await fetch(trainConfigUrl);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const trainCourses = await response.json();
                log(`获取培训配置成功，共${trainCourses.length}个课程`);

                // 更新全局变量
                currentTrainingCourses = trainCourses;
                courseIdToNameMapping = {};

                // 构建课程ID到名称的映射，并转换为UI需要的格式
                const courseConfigList = trainCourses.map(course => {
                    courseIdToNameMapping[course.course_id] = course.title;
                    log(`课程映射: ${course.course_id} -> ${course.title}`);

                    return {
                        courseId: course.course_id,
                        courseName: course.title,
                        resourceTotalCount: course.resource_count || 100 // 使用API返回的资源数量，如果没有则默认100
                    };
                });

                return courseConfigList;
            } catch (error) {
                log(`获取培训配置失败: ${error.message}`);
                throw error;
            }
        }













        // 主函数
        async function learningByCourseAndCount() {
            try {
                await join();
                const courseList = await getSelectedCourses();

                if (!courseList || courseList.length === 0) {
                    log('错误: 未选择任何课程进行学习');
                    return;
                }

                log(`开始处理选中的${courseList.length}个课程...`);

                for (const course of courseList) {
                    // 检查是否需要停止学习
                    if (shouldStop) {
                        log('学习已停止');
                        return;
                    }

                    try {
                        log(`\n开始学习: ${course.courseName}`);
                        log(`课程ID: ${course.courseId}`);
                        log(`资源总数: ${course.resourceTotalCount}`);

                        const resourceTotalCount = course.resourceTotalCount || 0;
                        log(`课程元数据显示资源总数: ${resourceTotalCount}`);

                        const courses = await getResourceByCourseId(course.courseId);

                        if (!courses || courses.length === 0) {
                            log(`警告: ${course.courseName} 未找到可学习的资源`);
                            continue;
                        }

                        log(`实际找到 ${courses.length} 个可学习的资源`);

                        // 获取该课程的学习数量配置
                        const courseResourceCount = getCourseResourceCount(course.courseId);
                        let num;
                        let learningStrategy;

                        if (courseResourceCount !== null) {
                            // 用户为该课程指定了数量
                            num = Math.min(courseResourceCount, courses.length);
                            learningStrategy = `课程配置 (${courseResourceCount}个)`;
                            if (courseResourceCount > courses.length) {
                                log(`注意: 课程配置学习${courseResourceCount}个资源，但实际只有${courses.length}个资源，将学习全部${num}个资源`);
                            }
                        } else {
                            // 用户未为该课程设置，学习所有实际资源
                            num = courses.length;
                            learningStrategy = '默认策略 (学习全部)';
                        }

                        log(`学习策略: ${learningStrategy}`);
                        log(`计划学习资源数: ${num}/${courses.length}`);

                        for (let i = 0; i < num && i < courses.length; i++) {
                            // 检查是否需要停止学习
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }

                            log(`正在学习第 ${i + 1}/${num} 个资源: ${courses[i].title || '未知资源'}`);

                            // 在开始学习资源前再次检查停止状态
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }

                            await learningOneResource(courses, courses[i].resourceId, course.courseId, course.frontCoverUrl);

                            // 在完成资源学习后检查停止状态
                            if (shouldStop) {
                                log('学习已停止');
                                return;
                            }
                        }

                        const studyDetail = await getStudyDetail(course.courseId);
                        if (studyDetail) {
                            const studyDetailJson = JSON.parse(studyDetail);
                            delete studyDetailJson.id;
                            delete studyDetailJson.client_id;
                            delete studyDetailJson.create_time;
                            delete studyDetailJson.update_time;
                            await async(JSON.stringify(studyDetailJson), course.courseId);
                        }
                        log(`完成课程: ${course.courseName}\n`);
                    } catch (error) {
                        log(`处理课程 ${course.courseName} 时发生错误: ${error.message}`);
                        // 继续处理下一个课程
                        continue;
                    }
                }
                log('所有课程处理完成！');
            } catch (error) {
                log(`错误: ${error.message}`);
                resetLearningState();
            }
        }

        // 切换学习状态的主控制函数
        async function toggleLearning() {
            if (isLearning) {
                stopLearning();
            } else {
                await startLearning();
            }
        }

        // 停止学习函数
        function stopLearning() {
            log('🛑 正在停止学习...');

            // 使用多线程学习管理器停止所有线程
            MultiThreadLearningManager.stopAllThreads();

            shouldStop = true;
            isLearning = false;
            updateButtonState();
            log('👤 用户手动停止学习');

            // 确保状态被正确设置
            setTimeout(() => {
                if (shouldStop && !isLearning) {
                    // 记录停止时间和计算耗时
                    if (learningStartTime) {
                        learningEndTime = new Date();
                        const endTimeStr = learningEndTime.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                        const totalDuration = learningEndTime - learningStartTime;
                        const durationStr = formatDuration(totalDuration);

                        log('✅ 学习已完全停止');
                        log(`⏰ 停止学习时间: ${endTimeStr}`);
                        log(`⏱️ 学习耗时: ${durationStr} (手动停止)`);
                    } else {
                        log('✅ 学习已完全停止');
                    }

                    // 显示停止时的统计信息
                    const stats = MultiThreadLearningManager.getStatus();
                    log(`📊 停止时统计: 总计${stats.total}个课程, 完成${stats.completed}个, 学习中${stats.learning}个, 已停止${stats.stopped}个, 错误${stats.error}个`);
                }
            }, 100);
        }

        // 更新按钮状态函数
        function updateButtonState() {
            const learningButton = document.getElementById('learningButton');
            const oneClickButton = document.getElementById('oneClickButton');

            if (isLearning) {
                learningButton.textContent = '停止学习';
                learningButton.className = 'stop-button';
                // 学习中时禁用一键学习按钮
                oneClickButton.disabled = true;
                oneClickButton.style.opacity = '0.5';
            } else {
                learningButton.textContent = '开始学习';
                learningButton.className = '';
                // 检查是否有JSONP数据来决定是否启用一键学习按钮
                updateOneClickButtonState();
            }
        }

        // 更新一键学习按钮状态
        function updateOneClickButtonState() {
            const oneClickButton = document.getElementById('oneClickButton');
            const jsonpInput = document.getElementById('jsonpInput');

            // 检查是否有JSONP数据
            if (jsonpInput && jsonpInput.value.trim() !== '') {
                oneClickButton.disabled = false;
                oneClickButton.style.opacity = '1';
            } else {
                oneClickButton.disabled = true;
                oneClickButton.style.opacity = '0.5';
            }
        }

        // 重置学习状态函数
        function resetLearningState() {
            isLearning = false;
            shouldStop = false;
            // 清理耗时记录
            learningStartTime = null;
            learningEndTime = null;
            updateButtonState();

            // 保持进度显示，不隐藏（用户可以查看最终状态）
            log('学习停止，保持进度显示以查看最终状态');
        }

        // 一键学习功能
        async function oneClickLearning() {
            log('🚀 启动一键学习模式...');

            try {
                // 检查是否已有课程配置界面
                const existingInputs = document.querySelectorAll('.course-config-input');

                if (existingInputs.length === 0) {
                    // 如果没有课程配置界面，需要先生成课程配置
                    log('📊 第一步：生成课程配置界面...');

                    // 处理用户提供的JSONP数据
                    const jsonpSuccess = processUserJsonpData();
                    if (!jsonpSuccess) {
                        log('❌ 请先在输入框中输入有效的JSONP响应数据');
                        alert('请先在输入框中输入JSONP响应数据，然后再使用一键学习');
                        return;
                    }

                    // 获取培训配置数据并生成课程配置UI
                    const trainCourses = await getTrainingConfig();
                    generateCourseConfigUI(trainCourses);
                    log(`已动态生成${trainCourses.length}个课程的配置界面`);

                    // 等待课程配置界面生成完成
                    log('⏳ 等待课程配置界面生成完成...');
                    await waitForCourseConfigUI();
                } else {
                    log('📊 检测到已有课程配置界面，跳过生成步骤');
                }

                // 第二步：应用到全部（使用默认设置）
                log('⚙️ 第二步：应用默认学习配置到全部课程...');
                applyDefaultToAll();

                // 等待一小段时间确保配置应用完成
                await new Promise(resolve => setTimeout(resolve, 500));

                // 验证配置是否仍然存在
                log('🔍 验证配置状态...');
                const courseInputs = document.querySelectorAll('.course-config-input');
                let configuredCount = 0;
                courseInputs.forEach(input => {
                    if (input.value && input.value.trim() !== '') {
                        configuredCount++;
                    }
                });
                log(`✅ 配置验证: ${configuredCount}个课程有具体数量配置`);

                // 第三步：开始学习
                log('🎯 第三步：开始多线程学习...');
                await startLearning(true); // 跳过清空日志

                log('✅ 一键学习流程完成！');

            } catch (error) {
                log(`❌ 一键学习失败: ${error.message}`);
                log('请检查JSONP数据是否正确输入，或手动执行各个步骤');
            }
        }

        // 等待课程配置界面生成完成
        async function waitForCourseConfigUI() {
            const maxWaitTime = 5000; // 最大等待5秒
            const checkInterval = 100; // 每100毫秒检查一次
            let waitedTime = 0;

            while (waitedTime < maxWaitTime) {
                const courseInputs = document.querySelectorAll('.course-config-input');
                if (courseInputs.length > 0) {
                    log(`✅ 课程配置界面已生成，找到${courseInputs.length}个课程配置项`);
                    return;
                }

                await new Promise(resolve => setTimeout(resolve, checkInterval));
                waitedTime += checkInterval;
            }

            throw new Error('等待课程配置界面生成超时，请手动执行各个步骤');
        }

        // 应用配置到全部课程（为一键学习功能服务，使用批量设置输入框的值）
        function applyDefaultToAll() {
            const batchInput = document.getElementById('batchResourceCount');
            const batchValue = batchInput ? batchInput.value.trim() : '';

            if (batchValue === '') {
                // 如果批量设置输入框为空，使用默认值5
                applySimpleDefaultToAll(5);
                return;
            }

            // 使用现有的setBatchConfig函数逻辑，但不显示alert
            log('📋 一键学习：使用批量设置输入框的配置...');
            log(`📝 批量设置输入值: "${batchValue}"`);

            // 复用setBatchConfig的逻辑
            setBatchConfigInternal(batchValue);
        }

        // 内部批量设置函数（不显示alert，供一键学习使用）
        function setBatchConfigInternal(value) {
            // 解析输入值：支持单个数字或逗号分隔的多个数字
            let numbers = [];
            if (value.includes(',')) {
                // 多个数字，按逗号分隔
                const parts = value.split(',');
                for (let part of parts) {
                    const trimmed = part.trim();
                    if (trimmed === '') {
                        numbers.push(null); // 空值表示学习全部
                    } else {
                        const num = parseInt(trimmed, 10);
                        if (isNaN(num) || num < 1) {
                            log(`❌ 输入格式错误: "${trimmed}" 不是有效的数字，跳过该值`);
                            continue;
                        }
                        numbers.push(num);
                    }
                }
            } else {
                // 单个数字
                const num = parseInt(value, 10);
                if (isNaN(num) || num < 1) {
                    log(`❌ 输入格式错误: "${value}" 不是有效的数字，使用默认值5`);
                    applySimpleDefaultToAll(5);
                    return;
                }
                numbers.push(num);
            }

            if (numbers.length === 0) {
                log('❌ 没有有效的数字，使用默认值5');
                applySimpleDefaultToAll(5);
                return;
            }

            // 获取所有课程输入框并按顺序应用设置
            const courseInputs = document.querySelectorAll('.course-config-input');
            let updatedCount = 0;
            let appliedSettings = [];

            courseInputs.forEach((courseInput, index) => {
                const courseId = courseInput.getAttribute('data-course-id');
                const courseName = courseInput.closest('.course-config-item').querySelector('.course-config-header').textContent;
                const maxResources = parseInt(courseInput.getAttribute('max'));

                // 获取对应的数字（如果数字不够，使用最后一个数字）
                const numberIndex = Math.min(index, numbers.length - 1);
                const count = numbers[numberIndex];

                if (count !== null && count > maxResources) {
                    // 如果设置的数量超过该课程的最大资源数，使用最大资源数
                    courseInput.value = maxResources;
                    CourseConfigManager.setCourseConfig(courseId, maxResources);
                    appliedSettings.push(`${courseName}: ${maxResources} (限制为最大值)`);
                } else {
                    courseInput.value = count || '';
                    CourseConfigManager.setCourseConfig(courseId, count);
                    appliedSettings.push(`${courseName}: ${count || '全部'}`);
                }
                updatedCount++;
            });

            // 显示详细的应用结果
            log(`📋 批量配置应用完成: ${updatedCount}个课程的学习数量已设置`);
            log(`📊 应用详情:`);
            appliedSettings.forEach(setting => {
                log(`  • ${setting}`);
            });

            if (numbers.length < courseInputs.length) {
                log(`💡 提示: 输入了${numbers.length}个数字，共${courseInputs.length}个课程。后面的课程使用最后一个数字 ${numbers[numbers.length - 1] || '全部'}`);
            }

            // 验证配置是否正确保存
            setTimeout(() => {
                log('🔍 验证配置保存状态...');
                let verifyCount = 0;
                courseInputs.forEach(courseInput => {
                    const courseId = courseInput.getAttribute('data-course-id');
                    const savedConfig = CourseConfigManager.getCourseConfig(courseId);
                    if (savedConfig !== null) {
                        verifyCount++;
                    }
                });
                log(`✅ 配置验证完成: ${verifyCount}个课程的配置已正确保存`);
            }, 100);
        }

        // 简单的默认配置应用（使用固定数值）
        function applySimpleDefaultToAll(defaultCount) {
            const courseInputs = document.querySelectorAll('.course-config-input');
            let updatedCount = 0;

            courseInputs.forEach(courseInput => {
                const courseId = courseInput.getAttribute('data-course-id');
                const maxResources = parseInt(courseInput.getAttribute('max'));

                // 使用默认数量，但不超过最大资源数
                const actualCount = Math.min(defaultCount, maxResources);

                courseInput.value = actualCount;
                CourseConfigManager.setCourseConfig(courseId, actualCount);
                updatedCount++;
            });

            log(`📋 默认配置应用完成: ${updatedCount}个课程设置为学习${defaultCount}个资源`);
        }





        // 启动函数
        async function startLearning(skipLogClear = false) {
            // 检查认证信息
            if (!userId || !macId || !macKey) {
                const errorMsg = '认证信息不完整，请先在输入框中输入JSONP数据';
                log(`❌ ${errorMsg}`);
                alert(errorMsg);
                return;
            }

            // 记录开始时间
            learningStartTime = new Date();
            const startTimeStr = learningStartTime.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // 设置学习状态
            isLearning = true;
            shouldStop = false;
            updateButtonState();

            // 清空日志（一键学习模式下跳过）
            if (!skipLogClear) {
                clearLog();
            }

            // 重置多线程管理器状态
            MultiThreadLearningManager.reset();

            log(`⏰ 开始学习时间: ${startTimeStr}`);

            try {
                // 使用公共函数解析JSONP数据
                parseJsonpData();

                // 显示学习配置信息
                log('📋 课程学习配置:');
                let hasCustomConfig = false;
                for (const [courseId, count] of CourseConfigManager.configs) {
                    if (count !== null) {
                        log(`  📌 课程 ${courseId}: ${count} 个资源`);
                        hasCustomConfig = true;
                    }
                }
                if (!hasCustomConfig) {
                    log('  📌 所有课程均使用默认策略 (学习全部资源)');
                }

                // 使用多线程学习管理器
                await MultiThreadLearningManager.startMultiThreadLearning();

                // 学习完成，重置状态
                if (!shouldStop) {
                    // 记录结束时间和计算总耗时
                    learningEndTime = new Date();
                    const endTimeStr = learningEndTime.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    const totalDuration = learningEndTime - learningStartTime;
                    const durationStr = formatDuration(totalDuration);

                    log('🎉 所有课程学习完成！');
                    log(`⏰ 结束学习时间: ${endTimeStr}`);
                    log(`⏱️ 总学习耗时: ${durationStr}`);

                    // 显示学习统计
                    const stats = MultiThreadLearningManager.getStatus();
                    log(`📊 学习统计: 总计${stats.total}个课程, 完成${stats.completed}个, 错误${stats.error}个, 停止${stats.stopped}个`);
                }
                resetLearningState();
            } catch (error) {
                log(`❌ 错误: ${error.message}`);
                log('请检查输入的JSONP数据格式是否正确');
                resetLearningState();
            }
        }
    </script>
</body>

</html>